import os
from collections import deque

# ---------- Visual Design Helpers ----------

def print_header(title):
    """Print a nicely formatted header"""
    width = 60
    print("\n" + "=" * width)
    print(f"{title:^{width}}")
    print("=" * width)

def print_subheader(title):
    """Print a nicely formatted subheader"""
    width = 50
    print("\n" + "-" * width)
    print(f"{title:^{width}}")
    print("-" * width)

def print_box(content, title=""):
    """Print content in a nice box"""
    lines = content.split('\n') if isinstance(content, str) else [str(content)]
    max_width = max(len(line) for line in lines) if lines else 0
    if title:
        max_width = max(max_width, len(title) + 4)
    
    width = max_width + 4
    print("┌" + "─" * (width - 2) + "┐")
    if title:
        print(f"│ {title:<{width-4}} │")
        print("├" + "─" * (width - 2) + "┤")
    
    for line in lines:
        print(f"│ {line:<{width-4}} │")
    print("└" + "─" * (width - 2) + "┘")

def clear_screen():
    """Clear the screen for better presentation"""
    os.system('cls' if os.name == 'nt' else 'clear')

# ---------- Finite Automaton Class ----------

class FiniteAutomaton:
    def __init__(self, states, alphabet, transitions, start_state, accept_states, use_epsilon=False):
        self.states = states
        self.alphabet = alphabet
        self.transitions = transitions  # dict: {(state, symbol): [next_states]}
        self.start_state = start_state
        self.accept_states = accept_states
        self.use_epsilon = use_epsilon

    def display(self):
        print_subheader("FINITE AUTOMATON")
        
        # Display basic info in boxes
        info_content = f"States: {', '.join(self.states)}\nAlphabet: {', '.join(self.alphabet)}"
        if self.use_epsilon:
            info_content += "\nε-transitions: Enabled"
        print_box(info_content, "FA Information")
        
        # Create transition table
        print("\n📋 TRANSITION TABLE:")
        headers = ["State/Symbol"] + self.alphabet + (['ε'] if self.use_epsilon else [])
        table_data = []
        
        for state in self.states:
            # Mark start state with → and accept states with *
            state_display = state
            if state == self.start_state:
                state_display = f"→ {state}"
            if state in self.accept_states:
                state_display = f"{state_display} *"
            
            row = [state_display]
            for symbol in self.alphabet + (['ε'] if self.use_epsilon else []):
                targets = self.transitions.get((state, symbol), [])
                cell = "{" + ", ".join(targets) + "}" if targets else "∅"
                row.append(cell)
            table_data.append(row)
        
        # Print table with borders
        print("┌" + "─" * (15 * len(headers) + len(headers) - 1) + "┐")
        header_row = "│" + "│".join(f"{h:^15}" for h in headers) + "│"
        print(header_row)
        print("├" + "┼".join("─" * 15 for _ in headers) + "┤")
        
        for row in table_data:
            data_row = "│" + "│".join(f"{cell:^15}" for cell in row) + "│"
            print(data_row)
        print("└" + "┴".join("─" * 15 for _ in headers) + "┘")
        
        # Display legend and summary
        legend = "Legend: → Start State, * Accept State, ∅ No Transition"
        summary = f"Start: {self.start_state} | Accept: {', '.join(self.accept_states) if self.accept_states else 'None'}"
        print(f"\n💡 {legend}")
        print(f"📊 {summary}")

def demo():
    clear_screen()
    print_header("🤖 FINITE AUTOMATON SIMULATOR DEMO 🤖")
    print_box("Welcome to the enhanced Finite Automaton program\nwith beautiful visual design and step-by-step visualization!", "Welcome")
    
    # Create a sample NFA
    states = ['q0', 'q1', 'q2']
    alphabet = ['a', 'b']
    transitions = {
        ('q0', 'a'): ['q0', 'q1'],
        ('q0', 'b'): ['q0'],
        ('q1', 'b'): ['q2'],
        ('q2', 'a'): ['q2'],
        ('q2', 'b'): ['q2']
    }
    start_state = 'q0'
    accept_states = ['q2']
    
    fa = FiniteAutomaton(states, alphabet, transitions, start_state, accept_states)
    
    print_header("SAMPLE NFA DISPLAY")
    fa.display()
    
    print_header("MAIN MENU PREVIEW")
    menu_options = [
        "🔧 FA Management (Create / Load / Save / Delete)",
        "📋 Display current FA", 
        "🧪 Test string on current FA",
        "🔍 Check FA type",
        "🔄 Convert NFA to DFA",
        "⚡ Minimize DFA",
        "🚪 Quit"
    ]
    
    for i, option in enumerate(menu_options, 1):
        print(f"   {i}. {option}")
    
    print("\n" + "─" * 60)
    
    print_box("✅ Visual enhancements completed!\n• Beautiful headers and boxes\n• Enhanced table formatting\n• Step-by-step conversion displays\n• Emoji icons for better UX\n• Clear visual feedback", "Demo Summary")

if __name__ == "__main__":
    demo()
