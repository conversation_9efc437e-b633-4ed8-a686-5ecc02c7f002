import mysql.connector
from collections import deque

# ========== Configuration ==========
DB_HOST = "localhost"
DB_USER = "root"          # change if you still use root
DB_PASSWORD = "Tharoeun@2456" # change to your password
DB_NAME = "automata_db"

# ---------- Finite Automaton Class ----------

class FiniteAutomaton:
    def __init__(self, states, alphabet, transitions, start_state, accept_states, use_epsilon=False):
        self.states = states
        self.alphabet = alphabet
        self.transitions = transitions  # dict: {(state, symbol): [next_states]}
        self.start_state = start_state
        self.accept_states = accept_states
        self.use_epsilon = use_epsilon

    def display(self):
        print("\n--- Finite Automaton ---")
        print(f"States: {self.states}")
        print(f"Alphabet: {self.alphabet}")
        if self.use_epsilon:
            print("Epsilon transitions: Enabled")
        print()
        
        # Simple table display
        header = ["State"] + self.alphabet + (['ε'] if self.use_epsilon else [])
        print(" | ".join(f"{h:^12}" for h in header))
        print("-" * (15 * len(header)))
        
        for state in self.states:
            display_state = state
            if state == self.start_state:
                display_state = f"-> {state}"
            if state in self.accept_states:
                display_state = f"{display_state}*"
            
            row = [f"{display_state:^12}"]
            for symbol in self.alphabet + (['ε'] if self.use_epsilon else []):
                targets = self.transitions.get((state, symbol), [])
                cell = "{" + ",".join(targets) + "}" if targets else "-"
                row.append(f"{cell:^12}")
            print(" | ".join(row))
        
        print(f"\nStart State: {self.start_state}")
        print(f"Accept States: {self.accept_states}")
        print("Note: -> indicates start state, * indicates accept state")

def demo_interface():
    print("Welcome to the Finite Automaton program with MySQL persistence!")
    
    # Create a sample FA
    states = ['q0', 'q1', 'q2']
    alphabet = ['a', 'b']
    transitions = {
        ('q0', 'a'): ['q0', 'q1'],
        ('q0', 'b'): ['q0'],
        ('q1', 'b'): ['q2'],
        ('q2', 'a'): ['q2'],
        ('q2', 'b'): ['q2']
    }
    start_state = 'q0'
    accept_states = ['q2']
    
    fa = FiniteAutomaton(states, alphabet, transitions, start_state, accept_states)
    
    while True:
        print("\n===== Main Menu =====")
        print("1. FA Management (Create / Load / Save / Delete)")
        print("2. Display current FA")
        print("3. Test string on current FA")
        print("4. Check FA type")
        print("5. Convert NFA to DFA")
        print("6. Minimize DFA")
        print("7. Quit")

        choice = input("Choose an option (1-7): ").strip()
        
        if choice == '2':
            fa.display()
        elif choice == '4':
            print("FA type: NFA")
        elif choice == '7':
            print("Goodbye!")
            break
        else:
            print("Demo - showing FA display and menu interface")
            fa.display()
            break

if __name__ == "__main__":
    demo_interface()
