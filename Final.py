import mysql.connector
from collections import deque

# ========== Configuration ==========
DB_HOST = "localhost"
DB_USER = "root"          # change if you still use root
DB_PASSWORD = "Tharoeun@2456" # change to your password
DB_NAME = "automata_db"

# ---------- MySQL Setup and Helpers ----------

def create_tables(conn):
    cursor = conn.cursor()
    cursor.execute("CREATE DATABASE IF NOT EXISTS automata_db;")
    cursor.execute("USE automata_db;")

    cursor.execute("""
    CREATE TABLE IF NOT EXISTS automata (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) UNIQUE NOT NULL
    );
    """)

    cursor.execute("""
    CREATE TABLE IF NOT EXISTS states (
        id INT AUTO_INCREMENT PRIMARY KEY,
        automaton_id INT,
        state_name VARCHAR(50),
        is_accept BOOLEAN,
        FOREIGN KEY (automaton_id) REFERENCES automata(id) ON DELETE CASCADE
    );
    """)

    cursor.execute("""
    CREATE TABLE IF NOT EXISTS transitions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        automaton_id INT,
        state_from VARCHAR(50),
        symbol VARCHAR(10),
        state_to VARCHAR(50),
        FOREIGN KEY (automaton_id) REFERENCES automata(id) ON DELETE CASCADE
    );
    """)

    cursor.execute("""
    CREATE TABLE IF NOT EXISTS metadata (
        automaton_id INT PRIMARY KEY,
        alphabet TEXT,
        start_state VARCHAR(50),
        use_epsilon BOOLEAN,
        FOREIGN KEY (automaton_id) REFERENCES automata(id) ON DELETE CASCADE
    );
    """)
    conn.commit()

def connect_db():
    return mysql.connector.connect(
        host=DB_HOST,
        user=DB_USER,
        password=DB_PASSWORD,
        database=DB_NAME
    )

# ---------- Finite Automaton Class ----------

class FiniteAutomaton:
    def __init__(self, states, alphabet, transitions, start_state, accept_states, use_epsilon=False):
        self.states = states
        self.alphabet = alphabet
        self.transitions = transitions  # dict: {(state, symbol): [next_states]}
        self.start_state = start_state
        self.accept_states = accept_states
        self.use_epsilon = use_epsilon

    def display(self):
        print("\n--- Finite Automaton ---")
        print(f"States: {self.states}")
        print(f"Alphabet: {self.alphabet}")
        if self.use_epsilon:
            print("Epsilon transitions: Enabled")
        print()

        # Simple table display
        header = ["State"] + self.alphabet + (['ε'] if self.use_epsilon else [])
        print(" | ".join(f"{h:^12}" for h in header))
        print("-" * (15 * len(header)))

        for state in self.states:
            display_state = state
            if state == self.start_state:
                display_state = f"-> {state}"
            if state in self.accept_states:
                display_state = f"{display_state}*"

            row = [f"{display_state:^12}"]
            for symbol in self.alphabet + (['ε'] if self.use_epsilon else []):
                targets = self.transitions.get((state, symbol), [])
                cell = "{" + ",".join(targets) + "}" if targets else "-"
                row.append(f"{cell:^12}")
            print(" | ".join(row))

        print(f"\nStart State: {self.start_state}")
        print(f"Accept States: {self.accept_states}")
        print("Note: -> indicates start state, * indicates accept state")

# ---------- FA Persistence Functions ----------

def save_fa_to_db(fa, conn, name):
    cursor = conn.cursor()
    # Check if name exists to avoid crash
    cursor.execute("SELECT id FROM automata WHERE name = %s", (name,))
    if cursor.fetchone():
        print(f"❗ FA with name '{name}' already exists. Please choose another name.")
        return

    # Insert automaton
    cursor.execute("INSERT INTO automata (name) VALUES (%s)", (name,))
    automaton_id = cursor.lastrowid

    # Insert states
    for s in fa.states:
        is_accept = 1 if s in fa.accept_states else 0
        cursor.execute(
            "INSERT INTO states (automaton_id, state_name, is_accept) VALUES (%s, %s, %s)",
            (automaton_id, s, is_accept)
        )

    # Insert transitions
    for (state, symbol), targets in fa.transitions.items():
        for t in targets:
            cursor.execute(
                "INSERT INTO transitions (automaton_id, state_from, symbol, state_to) VALUES (%s, %s, %s, %s)",
                (automaton_id, state, symbol, t)
            )

    # Insert metadata
    alphabet_str = ",".join(fa.alphabet)
    use_epsilon_int = 1 if fa.use_epsilon else 0
    cursor.execute(
        "INSERT INTO metadata (automaton_id, alphabet, start_state, use_epsilon) VALUES (%s, %s, %s, %s)",
        (automaton_id, alphabet_str, fa.start_state, use_epsilon_int)
    )

    conn.commit()
    print(f"✅ FA saved as '{name}' in database.")



def load_fa_from_db(conn, name):
    cursor = conn.cursor()
    cursor.execute("SELECT id FROM automata WHERE name=%s", (name,))
    row = cursor.fetchone()
    if not row:
        print("No automaton found with that name.")
        return None
    automaton_id = row[0]

    cursor.execute("SELECT state_name, is_accept FROM states WHERE automaton_id=%s", (automaton_id,))
    states_rows = cursor.fetchall()
    states = [r[0] for r in states_rows]
    accept_states = [r[0] for r in states_rows if r[1]]

    cursor.execute("SELECT state_from, symbol, state_to FROM transitions WHERE automaton_id=%s", (automaton_id,))
    transitions_rows = cursor.fetchall()
    transitions = {}
    for state_from, symbol, state_to in transitions_rows:
        transitions.setdefault((state_from, symbol), []).append(state_to)

    cursor.execute("SELECT alphabet, start_state, use_epsilon FROM metadata WHERE automaton_id=%s", (automaton_id,))
    meta = cursor.fetchone()
    if not meta:
        print("Metadata missing or corrupted.")
        return None
    alphabet_str, start_state, use_epsilon_int = meta
    alphabet = alphabet_str.split(",") if alphabet_str else []
    use_epsilon = bool(use_epsilon_int)

    return FiniteAutomaton(states, alphabet, transitions, start_state, accept_states, use_epsilon)

# ---------- Other FA Functions (same as before) ----------

def check_fa_type(transitions):
    for key, targets in transitions.items():
        if len(targets) != 1:
            return 'NFA'
    return 'DFA'

def epsilon_closure(state, transitions):
    stack = [state]
    closure = set([state])
    while stack:
        current = stack.pop()
        for next_state in transitions.get((current, 'ε'), []):
            if next_state not in closure:
                closure.add(next_state)
                stack.append(next_state)
    return closure

def simulate_fa(fa, input_string):
    current_states = set([fa.start_state])

    if fa.use_epsilon:
        # Start with ε-closure
        expanded = set()
        for s in current_states:
            expanded.update(epsilon_closure(s, fa.transitions))
        current_states = expanded

    for symbol in input_string:
        next_states = set()
        for state in current_states:
            targets = fa.transitions.get((state, symbol), [])
            next_states.update(targets)
        if fa.use_epsilon:
            # ε-closure after each transition
            expanded = set()
            for s in next_states:
                expanded.update(epsilon_closure(s, fa.transitions))
            current_states = expanded
        else:
            current_states = next_states

        if not current_states:
            break

    return any(state in fa.accept_states for state in current_states)



def nfa_to_dfa(states, alphabet, transitions, start, accepts):
    print("\n--- NFA to DFA Conversion ---")

    start_closure = frozenset(epsilon_closure(start, transitions))
    queue = deque([start_closure])
    dfa_states = set([start_closure])
    dfa_transitions = {}
    dfa_accepts = set()

    def name(s): return "{" + ",".join(sorted(s)) + "}"
    print(f"Step 1: Start state closure: {name(start_closure)}")

    step = 1
    while queue:
        current = queue.popleft()
        step += 1
        print(f"\nStep {step}: Processing state {name(current)}")

        for symbol in alphabet:
            move_result = set()
            for nfa_state in current:
                move_result.update(transitions.get((nfa_state, symbol), []))
            closure_result = set()
            for s in move_result:
                closure_result.update(epsilon_closure(s, transitions))
            if closure_result:
                closure_frozen = frozenset(closure_result)
                result_name = name(closure_frozen)
                dfa_transitions[(current, symbol)] = closure_frozen
                print(f"  δ({name(current)}, {symbol}) = {result_name}")

                if closure_frozen not in dfa_states:
                    dfa_states.add(closure_frozen)
                    queue.append(closure_frozen)
                    print(f"  -> New state: {result_name}")
            else:
                print(f"  δ({name(current)}, {symbol}) = ∅")

    # Determine accept states
    for state in dfa_states:
        if any(s in accepts for s in state):
            dfa_accepts.add(state)

    print(f"\nFinal DFA:")
    print("State".ljust(15) + " | " + " | ".join(f"{sym:^10}" for sym in alphabet))
    print("-" * (15 + 3 * len(alphabet) + 10 * len(alphabet)))

    for state_set in dfa_states:
        state_name = name(state_set)
        if state_name == name(start_closure):
            state_name = f"-> {state_name}"
        if state_set in dfa_accepts:
            state_name = f"{state_name}*"

        row = state_name.ljust(15) + " | "
        for symbol in alphabet:
            target = dfa_transitions.get((state_set, symbol))
            cell = name(target) if target else "∅"
            row += f"{cell:^10} | "
        print(row)

    named_states = [name(s) for s in dfa_states]
    named_transitions = {(name(k), sym): [name(v)] for (k, sym), v in dfa_transitions.items()}
    return named_states, alphabet, named_transitions, name(start_closure), [name(s) for s in dfa_accepts]

def minimize_dfa(states, alphabet, transitions, start, accepts):
    print("\n--- DFA Minimization ---")

    index = {s: i for i, s in enumerate(states)}
    n = len(states)
    distinguishable = [[False]*n for _ in range(n)]

    print("Step 1: Mark distinguishable pairs (accept vs non-accept)")
    for i in range(n):
        for j in range(i):
            si, sj = states[i], states[j]
            if (si in accepts) != (sj in accepts):
                distinguishable[i][j] = True
                print(f"  ({si}, {sj}) - different acceptance")

    print("\nStep 2: Mark pairs with different transitions")
    changed = True
    iteration = 0
    while changed:
        changed = False
        iteration += 1

        for i in range(n):
            for j in range(i):
                if distinguishable[i][j]:
                    continue
                for sym in alphabet:
                    ti = transitions.get((states[i], sym), [None])[0]
                    tj = transitions.get((states[j], sym), [None])[0]
                    if ti is None or tj is None:
                        continue
                    a, b = index[ti], index[tj]
                    a, b = max(a, b), min(a, b)
                    if distinguishable[a][b]:
                        distinguishable[i][j] = True
                        changed = True
                        print(f"  ({states[i]}, {states[j]}) - different via '{sym}'")
                        break

    print("\nStep 3: Group equivalent states")
    groups = []
    used = [False]*n
    for i in range(n):
        if used[i]:
            continue
        group = [states[i]]
        used[i] = True
        for j in range(i+1, n):
            if not distinguishable[max(i,j)][min(i,j)]:
                group.append(states[j])
                used[j] = True
        groups.append(group)

    for i, group in enumerate(groups):
        print(f"  Group {i+1}: {{{', '.join(group)}}}")

    # Create minimized DFA
    state_map = {s: g[0] for g in groups for s in g}
    min_states = [g[0] for g in groups]
    min_trans = {}
    for (s, sym), [t] in transitions.items():
        min_trans[(state_map[s], sym)] = [state_map[t]]
    min_start = state_map[start]
    min_accepts = list({state_map[s] for s in accepts})

    print(f"\nResult: {len(states)} states -> {len(min_states)} states")

    return min_states, alphabet, min_trans, min_start, min_accepts

# ---------- FA Designer ----------

def design_fa():
    print("\n=== Design Finite Automaton ===")

    # Get number of states
    while True:
        try:
            num_states = int(input("Enter number of states: "))
            if num_states <= 0:
                print("Please enter a positive number.")
                continue
            break
        except ValueError:
            print("Invalid input. Please enter a number.")

    states = [f"q{i}" for i in range(num_states)]
    print(f"Generated states: {states}")

    # Get alphabet
    while True:
        alphabet_input = input("Enter alphabet symbols (comma separated): ")
        alphabet = [a.strip() for a in alphabet_input.split(",") if a.strip()]
        if alphabet:
            break
        print("Alphabet cannot be empty.")

    # Ask about epsilon transitions
    use_epsilon = input("Use ε-transitions? (yes/no): ").lower().startswith('y')

    # Get transitions
    print("\nEnter transitions:")
    transitions = {}
    for state in states:
        for symbol in alphabet + (['ε'] if use_epsilon else []):
            while True:
                raw_input = input(f"δ({state}, {symbol}) → ").split(",")
                targets = [t.strip() for t in raw_input if t.strip()]
                if any(t not in states for t in targets):
                    print(f"⚠ Invalid targets. Valid states: {states}")
                else:
                    if targets:
                        transitions[(state, symbol)] = targets
                    break

    # Get start state
    while True:
        start_state = input(f"Enter start state (from {states}): ").strip()
        if start_state in states:
            break
        print("Invalid start state.")

    # Get accept states
    accept_states = input(f"Enter accept states (comma separated from {states}): ").split(",")
    accept_states = [a.strip() for a in accept_states if a.strip() in states]

    fa = FiniteAutomaton(states, alphabet, transitions, start_state, accept_states, use_epsilon=use_epsilon)
    fa.display()
    return fa

# ---------- Main Program Loop ----------

def main():
    try:
        conn = connect_db()
    except mysql.connector.Error as err:
        print(f"Error connecting to MySQL: {err}")
        return

    create_tables(conn)

    fa = None
    print("Welcome to the Finite Automaton program with MySQL persistence!")

    while True:
        print("\n===== Main Menu =====")
        print("1. FA Management (Create / Load / Save / Delete)")
        print("2. Display current FA")
        print("3. Test string on current FA")
        print("4. Check FA type")
        print("5. Convert NFA to DFA")
        print("6. Minimize DFA")
        print("7. Quit")

        choice = input("Choose an option (1-7): ").strip()

        if choice == '1':
            while True:
                print("\n--- FA Management ---")
                print("1. Create new FA")
                print("2. Load FA from database")
                print("3. Delete FA from database")
                print("4. Save current FA to database")
                print("5. Back to Main Menu")
                sub_choice = input("Choose an option (1-5): ").strip()

                if sub_choice == '1':
                    fa = design_fa()

                elif sub_choice == '2':
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM automata")
                    all_names = [row[0] for row in cursor.fetchall()]
                    if not all_names:
                        print("No FA saved in database.")
                        continue

                    print("Available FA names:", ", ".join(all_names))
                    name = input("Enter the name of the FA to load: ")
                    loaded_fa = load_fa_from_db(conn, name)
                    if loaded_fa:
                        fa = loaded_fa
                        print(f"FA '{name}' loaded:")
                        fa.display()

                elif sub_choice == '3':
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM automata")
                    all_names = [row[0] for row in cursor.fetchall()]
                    if not all_names:
                        print("No FA saved in database.")
                        continue

                    print("Available FA names:", ", ".join(all_names))
                    name = input("Enter the name of the FA to delete: ")
                    confirm = input(f"Are you sure you want to delete FA '{name}'? (y/n): ").strip().lower()
                    if confirm == 'y':
                        cursor.execute("DELETE FROM automata WHERE name = %s", (name,))
                        conn.commit()
                        print(f"FA '{name}' deleted successfully.")
                    else:
                        print("Delete cancelled.")

                elif sub_choice == '4':
                    if not fa:
                        print("No FA created or loaded.")
                        continue

                    while True:
                        name = input("Enter a name to save the current FA: ")
                        cursor = conn.cursor()
                        cursor.execute("SELECT id FROM automata WHERE name = %s", (name,))
                        if cursor.fetchone():
                            print(f"❗ FA with name '{name}' already exists. Please choose another name.")
                        else:
                            save_fa_to_db(fa, conn, name)
                            break

                elif sub_choice == '5':
                    break

                else:
                    print("Invalid option. Please choose 1-5.")

        elif choice == '2':
            if fa:
                fa.display()
            else:
                print("No FA loaded or created yet.")

        elif choice == '3':
            if not fa:
                print("No FA loaded or created yet.")
                continue

            while True:
                input_string = input("Enter input string to test (or type 'exit' to stop): ")
                if input_string.lower() in ['exit', 'quit', 'q', 'no']:
                    print("Stopping string testing.")
                    break

                if simulate_fa(fa, input_string):
                    print(f"The input string '{input_string}' is **ACCEPTED** by the FA.")
                else:
                    print(f"The input string '{input_string}' is **REJECTED** by the FA.")



        elif choice == '4':
            if fa:
                print("FA type:", check_fa_type(fa.transitions))
            else:
                print("No FA loaded or created yet.")

        elif choice == '5':
            if not fa:
                print("No FA loaded or created yet.")
                continue
            if check_fa_type(fa.transitions) == 'DFA':
                print("FA is already a DFA.")
                continue
            dfa_states, dfa_alphabet, dfa_trans, dfa_start, dfa_accepts = nfa_to_dfa(
                fa.states, fa.alphabet, fa.transitions, fa.start_state, fa.accept_states)
            fa = FiniteAutomaton(dfa_states, dfa_alphabet, dfa_trans, dfa_start, dfa_accepts)
            print("Converted to DFA:")
            fa.display()

        elif choice == '6':
            if not fa:
                print("No FA loaded or created yet.")
                continue
            if check_fa_type(fa.transitions) != 'DFA':
                print("FA must be a DFA to minimize.")
                continue
            min_states, min_alpha, min_trans, min_start, min_accepts = minimize_dfa(
                fa.states, fa.alphabet, fa.transitions, fa.start_state, fa.accept_states)
            fa = FiniteAutomaton(min_states, min_alpha, min_trans, min_start, min_accepts)
            print("Minimized DFA:")
            fa.display()

        elif choice == '7':
            print("Goodbye!")
            break

        else:
            print("Invalid option. Please choose between 1-7.")

    conn.close()



if __name__ == "__main__":
    main()
