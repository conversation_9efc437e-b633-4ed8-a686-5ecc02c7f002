import mysql.connector
from collections import deque
import os

# ========== Configuration ==========
DB_HOST = "localhost"
DB_USER = "root"          # change if you still use root
DB_PASSWORD = "Tharoeun@2456" # change to your password
DB_NAME = "automata_db"


# ---------- Visual Design Helpers ----------

def print_header(title):
    """Print a nicely formatted header"""
    width = 60
    print("\n" + "=" * width)
    print(f"{title:^{width}}")
    print("=" * width)

def print_subheader(title):
    """Print a nicely formatted subheader"""
    width = 50
    print("\n" + "-" * width)
    print(f"{title:^{width}}")
    print("-" * width)

def print_box(content, title=""):
    """Print content in a nice box"""
    lines = content.split('\n') if isinstance(content, str) else [str(content)]
    max_width = max(len(line) for line in lines) if lines else 0
    if title:
        max_width = max(max_width, len(title) + 4)

    width = max_width + 4
    print("┌" + "─" * (width - 2) + "┐")
    if title:
        print(f"│ {title:<{width-4}} │")
        print("├" + "─" * (width - 2) + "┤")

    for line in lines:
        print(f"│ {line:<{width-4}} │")
    print("└" + "─" * (width - 2) + "┘")

def clear_screen():
    """Clear the screen for better presentation"""
    os.system('cls' if os.name == 'nt' else 'clear')

# ---------- MySQL Setup and Helpers ----------

def create_tables(conn):
    cursor = conn.cursor()
    cursor.execute("CREATE DATABASE IF NOT EXISTS automata_db;")
    cursor.execute("USE automata_db;")

    cursor.execute("""
    CREATE TABLE IF NOT EXISTS automata (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) UNIQUE NOT NULL
    );
    """)

    cursor.execute("""
    CREATE TABLE IF NOT EXISTS states (
        id INT AUTO_INCREMENT PRIMARY KEY,
        automaton_id INT,
        state_name VARCHAR(50),
        is_accept BOOLEAN,
        FOREIGN KEY (automaton_id) REFERENCES automata(id) ON DELETE CASCADE
    );
    """)

    cursor.execute("""
    CREATE TABLE IF NOT EXISTS transitions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        automaton_id INT,
        state_from VARCHAR(50),
        symbol VARCHAR(10),
        state_to VARCHAR(50),
        FOREIGN KEY (automaton_id) REFERENCES automata(id) ON DELETE CASCADE
    );
    """)

    cursor.execute("""
    CREATE TABLE IF NOT EXISTS metadata (
        automaton_id INT PRIMARY KEY,
        alphabet TEXT,
        start_state VARCHAR(50),
        use_epsilon BOOLEAN,
        FOREIGN KEY (automaton_id) REFERENCES automata(id) ON DELETE CASCADE
    );
    """)
    conn.commit()

def connect_db():
    return mysql.connector.connect(
        host="localhost",
        user="root",
        password="root",
        database="automata_db"
    )

# ---------- Finite Automaton Class ----------

class FiniteAutomaton:
    def __init__(self, states, alphabet, transitions, start_state, accept_states, use_epsilon=False):
        self.states = states
        self.alphabet = alphabet
        self.transitions = transitions  # dict: {(state, symbol): [next_states]}
        self.start_state = start_state
        self.accept_states = accept_states
        self.use_epsilon = use_epsilon

    def display(self):
        print_subheader("FINITE AUTOMATON")

        # Display basic info in boxes
        info_content = f"States: {', '.join(self.states)}\nAlphabet: {', '.join(self.alphabet)}"
        if self.use_epsilon:
            info_content += "\nε-transitions: Enabled"
        print_box(info_content, "FA Information")

        # Create transition table
        print("\n📋 TRANSITION TABLE:")
        headers = ["State/Symbol"] + self.alphabet + (['ε'] if self.use_epsilon else [])
        table_data = []

        for state in self.states:
            # Mark start state with → and accept states with *
            state_display = state
            if state == self.start_state:
                state_display = f"→ {state}"
            if state in self.accept_states:
                state_display = f"{state_display} *"

            row = [state_display]
            for symbol in self.alphabet + (['ε'] if self.use_epsilon else []):
                targets = self.transitions.get((state, symbol), [])
                cell = "{" + ", ".join(targets) + "}" if targets else "∅"
                row.append(cell)
            table_data.append(row)

        # Print table with borders
        print("┌" + "─" * (15 * len(headers) + len(headers) - 1) + "┐")
        header_row = "│" + "│".join(f"{h:^15}" for h in headers) + "│"
        print(header_row)
        print("├" + "┼".join("─" * 15 for _ in headers) + "┤")

        for row in table_data:
            data_row = "│" + "│".join(f"{cell:^15}" for cell in row) + "│"
            print(data_row)
        print("└" + "┴".join("─" * 15 for _ in headers) + "┘")

        # Display legend and summary
        legend = "Legend: → Start State, * Accept State, ∅ No Transition"
        summary = f"Start: {self.start_state} | Accept: {', '.join(self.accept_states) if self.accept_states else 'None'}"
        print(f"\n💡 {legend}")
        print(f"📊 {summary}")

# ---------- FA Persistence Functions ----------

def save_fa_to_db(fa, conn, name):
    cursor = conn.cursor()
    # Check if name exists to avoid crash
    cursor.execute("SELECT id FROM automata WHERE name = %s", (name,))
    if cursor.fetchone():
        print(f"❗ FA with name '{name}' already exists. Please choose another name.")
        return

    # Insert automaton
    cursor.execute("INSERT INTO automata (name) VALUES (%s)", (name,))
    automaton_id = cursor.lastrowid

    # Insert states
    for s in fa.states:
        is_accept = 1 if s in fa.accept_states else 0
        cursor.execute(
            "INSERT INTO states (automaton_id, state_name, is_accept) VALUES (%s, %s, %s)",
            (automaton_id, s, is_accept)
        )

    # Insert transitions
    for (state, symbol), targets in fa.transitions.items():
        for t in targets:
            cursor.execute(
                "INSERT INTO transitions (automaton_id, state_from, symbol, state_to) VALUES (%s, %s, %s, %s)",
                (automaton_id, state, symbol, t)
            )

    # Insert metadata
    alphabet_str = ",".join(fa.alphabet)
    use_epsilon_int = 1 if fa.use_epsilon else 0
    cursor.execute(
        "INSERT INTO metadata (automaton_id, alphabet, start_state, use_epsilon) VALUES (%s, %s, %s, %s)",
        (automaton_id, alphabet_str, fa.start_state, use_epsilon_int)
    )

    conn.commit()
    print(f"✅ FA saved as '{name}' in database.")



def load_fa_from_db(conn, name):
    cursor = conn.cursor()
    cursor.execute("SELECT id FROM automata WHERE name=%s", (name,))
    row = cursor.fetchone()
    if not row:
        print("No automaton found with that name.")
        return None
    automaton_id = row[0]

    cursor.execute("SELECT state_name, is_accept FROM states WHERE automaton_id=%s", (automaton_id,))
    states_rows = cursor.fetchall()
    states = [r[0] for r in states_rows]
    accept_states = [r[0] for r in states_rows if r[1]]

    cursor.execute("SELECT state_from, symbol, state_to FROM transitions WHERE automaton_id=%s", (automaton_id,))
    transitions_rows = cursor.fetchall()
    transitions = {}
    for state_from, symbol, state_to in transitions_rows:
        transitions.setdefault((state_from, symbol), []).append(state_to)

    cursor.execute("SELECT alphabet, start_state, use_epsilon FROM metadata WHERE automaton_id=%s", (automaton_id,))
    meta = cursor.fetchone()
    if not meta:
        print("Metadata missing or corrupted.")
        return None
    alphabet_str, start_state, use_epsilon_int = meta
    alphabet = alphabet_str.split(",") if alphabet_str else []
    use_epsilon = bool(use_epsilon_int)

    return FiniteAutomaton(states, alphabet, transitions, start_state, accept_states, use_epsilon)

# ---------- Other FA Functions (same as before) ----------

def check_fa_type(transitions):
    for key, targets in transitions.items():
        if len(targets) != 1:
            return 'NFA'
    return 'DFA'

def epsilon_closure(state, transitions):
    stack = [state]
    closure = set([state])
    while stack:
        current = stack.pop()
        for next_state in transitions.get((current, 'ε'), []):
            if next_state not in closure:
                closure.add(next_state)
                stack.append(next_state)
    return closure

def simulate_fa(fa, input_string):
    current_states = set([fa.start_state])

    if fa.use_epsilon:
        # Start with ε-closure
        expanded = set()
        for s in current_states:
            expanded.update(epsilon_closure(s, fa.transitions))
        current_states = expanded

    for symbol in input_string:
        next_states = set()
        for state in current_states:
            targets = fa.transitions.get((state, symbol), [])
            next_states.update(targets)
        if fa.use_epsilon:
            # ε-closure after each transition
            expanded = set()
            for s in next_states:
                expanded.update(epsilon_closure(s, fa.transitions))
            current_states = expanded
        else:
            current_states = next_states

        if not current_states:
            break

    return any(state in fa.accept_states for state in current_states)



def nfa_to_dfa(states, alphabet, transitions, start, accepts):
    print_subheader("NFA TO DFA CONVERSION PROCESS")

    start_closure = frozenset(epsilon_closure(start, transitions))
    queue = deque([start_closure])
    dfa_states = set([start_closure])
    dfa_transitions = {}
    dfa_accepts = set()

    step = 1
    print(f"\n🔄 Step {step}: Initial state")
    def name(s): return "{" + ",".join(sorted(s)) + "}"
    print(f"   Start state closure: {name(start_closure)}")

    # Show step-by-step conversion
    conversion_steps = []

    while queue:
        current = queue.popleft()
        step += 1
        print(f"\n🔄 Step {step}: Processing state {name(current)}")

        step_data = {"state": name(current), "transitions": {}}

        for symbol in alphabet:
            move_result = set()
            for nfa_state in current:
                move_result.update(transitions.get((nfa_state, symbol), []))
            closure_result = set()
            for s in move_result:
                closure_result.update(epsilon_closure(s, transitions))
            if closure_result:
                closure_frozen = frozenset(closure_result)
                result_name = name(closure_frozen)
                dfa_transitions[(current, symbol)] = closure_frozen
                step_data["transitions"][symbol] = result_name
                print(f"   δ({name(current)}, {symbol}) = {result_name}")

                if closure_frozen not in dfa_states:
                    dfa_states.add(closure_frozen)
                    queue.append(closure_frozen)
                    print(f"   ✨ New state discovered: {result_name}")
            else:
                step_data["transitions"][symbol] = "∅"
                print(f"   δ({name(current)}, {symbol}) = ∅")

        conversion_steps.append(step_data)

    # Determine accept states
    for state in dfa_states:
        if any(s in accepts for s in state):
            dfa_accepts.add(state)

    # Show final conversion table
    print(f"\n📋 FINAL DFA TRANSITION TABLE:")
    headers = ["State"] + alphabet
    table_data = []

    for state_set in dfa_states:
        state_name = name(state_set)
        if state_name == name(start_closure):
            state_name = f"→ {state_name}"
        if state_set in dfa_accepts:
            state_name = f"{state_name} *"

        row = [state_name]
        for symbol in alphabet:
            target = dfa_transitions.get((state_set, symbol))
            cell = name(target) if target else "∅"
            row.append(cell)
        table_data.append(row)

    # Print final table
    print("┌" + "─" * (20 * len(headers) + len(headers) - 1) + "┐")
    header_row = "│" + "│".join(f"{h:^20}" for h in headers) + "│"
    print(header_row)
    print("├" + "┼".join("─" * 20 for _ in headers) + "┤")

    for row in table_data:
        data_row = "│" + "│".join(f"{cell:^20}" for cell in row) + "│"
        print(data_row)
    print("└" + "┴".join("─" * 20 for _ in headers) + "┘")

    named_states = [name(s) for s in dfa_states]
    named_transitions = {(name(k), sym): [name(v)] for (k, sym), v in dfa_transitions.items()}
    return named_states, alphabet, named_transitions, name(start_closure), [name(s) for s in dfa_accepts]

def minimize_dfa(states, alphabet, transitions, start, accepts):
    print_subheader("DFA MINIMIZATION PROCESS")

    index = {s: i for i, s in enumerate(states)}
    n = len(states)
    distinguishable = [[False]*n for _ in range(n)]

    print("\n🔍 Step 1: Mark distinguishable pairs (accept vs non-accept)")
    initial_pairs = []
    for i in range(n):
        for j in range(i):
            si, sj = states[i], states[j]
            if (si in accepts) != (sj in accepts):
                distinguishable[i][j] = True
                initial_pairs.append((si, sj))

    if initial_pairs:
        print("   Initially distinguishable pairs:")
        for pair in initial_pairs:
            print(f"   • ({pair[0]}, {pair[1]})")
    else:
        print("   No initially distinguishable pairs found.")

    print("\n🔍 Step 2: Iteratively mark more distinguishable pairs")
    iteration = 0
    changed = True
    while changed:
        changed = False
        iteration += 1
        new_pairs = []

        for i in range(n):
            for j in range(i):
                if distinguishable[i][j]:
                    continue
                for sym in alphabet:
                    ti = transitions.get((states[i], sym), [None])[0]
                    tj = transitions.get((states[j], sym), [None])[0]
                    if ti is None or tj is None:
                        continue
                    a, b = index[ti], index[tj]
                    a, b = max(a, b), min(a, b)
                    if distinguishable[a][b]:
                        distinguishable[i][j] = True
                        changed = True
                        new_pairs.append((states[i], states[j], sym))
                        break

        if new_pairs:
            print(f"   Iteration {iteration}: New distinguishable pairs:")
            for pair in new_pairs:
                print(f"   • ({pair[0]}, {pair[1]}) via symbol '{pair[2]}'")
        elif iteration == 1:
            print(f"   Iteration {iteration}: No new pairs found.")

    print("\n🔍 Step 3: Group equivalent states")
    groups = []
    used = [False]*n
    for i in range(n):
        if used[i]:
            continue
        group = [states[i]]
        used[i] = True
        for j in range(i+1, n):
            if not distinguishable[max(i,j)][min(i,j)]:
                group.append(states[j])
                used[j] = True
        groups.append(group)

    print("   Equivalent state groups:")
    for i, group in enumerate(groups):
        print(f"   Group {i+1}: {{{', '.join(group)}}}")

    # Create minimized DFA
    state_map = {s: g[0] for g in groups for s in g}
    min_states = [g[0] for g in groups]
    min_trans = {}
    for (s, sym), [t] in transitions.items():
        min_trans[(state_map[s], sym)] = [state_map[t]]
    min_start = state_map[start]
    min_accepts = list({state_map[s] for s in accepts})

    print(f"\n📊 Minimization Result:")
    print(f"   Original states: {len(states)} → Minimized states: {len(min_states)}")
    print(f"   States reduced by: {len(states) - len(min_states)}")

    return min_states, alphabet, min_trans, min_start, min_accepts

# ---------- FA Designer ----------

def design_fa():
    print_header("🎨 DESIGN FINITE AUTOMATON")
    print_box("Let's create your finite automaton step by step!", "Getting Started")

    # Get number of states
    while True:
        try:
            num_states = int(input("🔢 Enter number of states: "))
            if num_states <= 0:
                print("   ❌ Please enter a positive number.")
                continue
            break
        except ValueError:
            print("   ❌ Invalid input. Please enter a number.")

    states = [f"q{i}" for i in range(num_states)]
    print_box(f"Generated states: {', '.join(states)}", "States Created")

    # Get alphabet
    while True:
        alphabet_input = input("🔤 Enter alphabet symbols (comma separated): ")
        alphabet = [a.strip() for a in alphabet_input.split(",") if a.strip()]
        if alphabet:
            break
        print("   ❌ Alphabet cannot be empty.")

    print_box(f"Alphabet: {', '.join(alphabet)}", "Alphabet Set")

    # Ask about epsilon transitions
    use_epsilon = input("🔄 Use ε-transitions? (yes/no): ").lower().startswith('y')
    if use_epsilon:
        print("   ✅ ε-transitions enabled")
    else:
        print("   ❌ ε-transitions disabled")

    # Get transitions
    print_subheader("🔗 TRANSITION DEFINITION")
    print("💡 For each state-symbol pair, enter target states (comma separated)")
    print("   Leave empty if no transition exists\n")

    transitions = {}
    for state in states:
        print(f"📍 Defining transitions for state {state}:")
        for symbol in alphabet + (['ε'] if use_epsilon else []):
            while True:
                raw_input = input(f"   δ({state}, {symbol}) → ").strip()
                if not raw_input:
                    break  # No transition
                targets = [t.strip() for t in raw_input.split(",") if t.strip()]
                if any(t not in states for t in targets):
                    print(f"   ⚠️  Invalid targets. Valid states: {states}")
                else:
                    if targets:
                        transitions[(state, symbol)] = targets
                    break

    # Get start state
    while True:
        start_state = input(f"🚀 Enter start state (from {states}): ").strip()
        if start_state in states:
            break
        print("   ❌ Invalid start state.")

    # Get accept states
    accept_input = input(f"🎯 Enter accept states (comma separated from {states}): ")
    accept_states = [a.strip() for a in accept_input.split(",") if a.strip() in states]

    print_box("✅ FA creation completed!", "Success")
    fa = FiniteAutomaton(states, alphabet, transitions, start_state, accept_states, use_epsilon=use_epsilon)
    print("\n🎯 Your created FA:")
    fa.display()
    return fa

# ---------- Main Program Loop ----------

def main():
    try:
        conn = connect_db()
    except mysql.connector.Error as err:
        print(f"❌ Error connecting to MySQL: {err}")
        return

    create_tables(conn)

    fa = None
    clear_screen()
    print_header("🤖 FINITE AUTOMATON SIMULATOR 🤖")
    print_box("Welcome to the advanced Finite Automaton program\nwith MySQL persistence and step-by-step visualization!", "Welcome")

    while True:
        print_header("MAIN MENU")
        menu_options = [
            "🔧 FA Management (Create / Load / Save / Delete)",
            "📋 Display current FA",
            "🧪 Test string on current FA",
            "🔍 Check FA type",
            "🔄 Convert NFA to DFA",
            "⚡ Minimize DFA",
            "🚪 Quit"
        ]

        for i, option in enumerate(menu_options, 1):
            print(f"   {i}. {option}")

        print("\n" + "─" * 60)
        choice = input("🎯 Choose an option (1-7): ").strip()

        if choice == '1':
            while True:
                print_subheader("🔧 FA MANAGEMENT")
                management_options = [
                    "➕ Create new FA",
                    "📂 Load FA from database",
                    "🗑️  Delete FA from database",
                    "💾 Save current FA to database",
                    "🔙 Back to Main Menu"
                ]

                for i, option in enumerate(management_options, 1):
                    print(f"   {i}. {option}")

                print("\n" + "─" * 50)
                sub_choice = input("🎯 Choose an option (1-5): ").strip()

                if sub_choice == '1':
                    fa = design_fa()

                elif sub_choice == '2':
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM automata")
                    all_names = [row[0] for row in cursor.fetchall()]
                    if not all_names:
                        print_box("❌ No FA saved in database.", "Database Empty")
                        continue

                    print_box(f"Available FAs: {', '.join(all_names)}", "Database Contents")
                    name = input("📂 Enter the name of the FA to load: ")
                    loaded_fa = load_fa_from_db(conn, name)
                    if loaded_fa:
                        fa = loaded_fa
                        print_box(f"✅ FA '{name}' loaded successfully!", "Success")
                        print("\n🎯 Loaded FA:")
                        fa.display()

                elif sub_choice == '3':
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM automata")
                    all_names = [row[0] for row in cursor.fetchall()]
                    if not all_names:
                        print_box("❌ No FA saved in database.", "Database Empty")
                        continue

                    print_box(f"Available FAs: {', '.join(all_names)}", "Database Contents")
                    name = input("🗑️  Enter the name of the FA to delete: ")
                    confirm = input(f"⚠️  Are you sure you want to delete FA '{name}'? (y/n): ").strip().lower()
                    if confirm == 'y':
                        cursor.execute("DELETE FROM automata WHERE name = %s", (name,))
                        conn.commit()
                        print_box(f"✅ FA '{name}' deleted successfully.", "Success")
                    else:
                        print_box("❌ Delete operation cancelled.", "Cancelled")

                elif sub_choice == '4':
                    if not fa:
                        print_box("❌ No FA created or loaded.", "Error")
                        continue

                    while True:
                        name = input("💾 Enter a name to save the current FA: ")
                        cursor = conn.cursor()
                        cursor.execute("SELECT id FROM automata WHERE name = %s", (name,))
                        if cursor.fetchone():
                            print(f"   ❗ FA with name '{name}' already exists. Please choose another name.")
                        else:
                            save_fa_to_db(fa, conn, name)
                            break

                elif sub_choice == '5':
                    break

                else:
                    print("Invalid option. Please choose 1-5.")

        elif choice == '2':
            if fa:
                fa.display()
            else:
                print_box("❌ No FA loaded or created yet.", "Error")

        elif choice == '3':
            if not fa:
                print_box("❌ No FA loaded or created yet.", "Error")
                continue

            print_subheader("🧪 STRING TESTING")
            print("💡 Test strings against your finite automaton")
            print("   Type 'exit', 'quit', 'q', or 'no' to stop testing\n")

            while True:
                input_string = input("🔤 Enter input string to test: ").strip()
                if input_string.lower() in ['exit', 'quit', 'q', 'no']:
                    print("🔚 Stopping string testing.")
                    break

                result = simulate_fa(fa, input_string)
                if result:
                    print_box(f"✅ ACCEPTED\nThe string '{input_string}' is accepted by the FA.", "Result")
                else:
                    print_box(f"❌ REJECTED\nThe string '{input_string}' is rejected by the FA.", "Result")
                print()



        elif choice == '4':
            if fa:
                fa_type = check_fa_type(fa.transitions)
                type_icon = "🔄" if fa_type == "NFA" else "⚡"
                print_box(f"{type_icon} FA Type: {fa_type}", "Analysis Result")
            else:
                print_box("❌ No FA loaded or created yet.", "Error")

        elif choice == '5':
            if not fa:
                print_box("❌ No FA loaded or created yet.", "Error")
                continue
            if check_fa_type(fa.transitions) == 'DFA':
                print_box("ℹ️  FA is already a DFA. No conversion needed.", "Information")
                continue

            print("🔄 Starting NFA to DFA conversion...")
            dfa_states, dfa_alphabet, dfa_trans, dfa_start, dfa_accepts = nfa_to_dfa(
                fa.states, fa.alphabet, fa.transitions, fa.start_state, fa.accept_states)
            fa = FiniteAutomaton(dfa_states, dfa_alphabet, dfa_trans, dfa_start, dfa_accepts)
            print_box("✅ Conversion completed successfully!", "Success")
            print("\n🎯 Your converted DFA:")
            fa.display()

        elif choice == '6':
            if not fa:
                print_box("❌ No FA loaded or created yet.", "Error")
                continue
            if check_fa_type(fa.transitions) != 'DFA':
                print_box("❌ FA must be a DFA to minimize.\nPlease convert NFA to DFA first.", "Error")
                continue

            print("⚡ Starting DFA minimization...")
            min_states, min_alpha, min_trans, min_start, min_accepts = minimize_dfa(
                fa.states, fa.alphabet, fa.transitions, fa.start_state, fa.accept_states)
            fa = FiniteAutomaton(min_states, min_alpha, min_trans, min_start, min_accepts)
            print_box("✅ Minimization completed successfully!", "Success")
            print("\n🎯 Your minimized DFA:")
            fa.display()

        elif choice == '7':
            print_header("👋 GOODBYE!")
            print_box("Thank you for using the Finite Automaton Simulator!\nHave a great day! 🌟", "Farewell")
            break

        else:
            print_box("❌ Invalid option. Please choose between 1-7.", "Error")

    conn.close()



if __name__ == "__main__":
    main()
